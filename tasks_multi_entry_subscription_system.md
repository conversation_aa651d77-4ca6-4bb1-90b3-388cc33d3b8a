# Multi-Entry Subscription System Implementation

## Overview
Redesign the user-subscription system to support multiple subscription sources (Stripe, perks, giveaways) with proper precedence, status tracking, and Stripe subscription pausing when perk-based subscriptions are active.

## Current State Analysis
- Single UserSubscription document per user at `userSubscriptions/{userId}`
- Direct Stripe integration with single subscription tracking
- Perk system exists but subscription perks are applied by extending current subscription
- No precedence system for multiple subscription sources
- No status tracking for different subscription types

## Target Architecture
- Multiple UserSubscriptionEntry documents at `userSubscriptions/{userId}/entries/{entryId}`
- Precedence-based system: Perk > Giveaway > Stripe
- Status tracking: `applied`, `pending`, `paused`, `expired`
- Stripe subscription pausing when higher precedence subscriptions are active
- Cron job for expiration management

## Tasks

### Phase 1: Schema Design & Types
- [ ] Design new UserSubscriptionEntry interface with source tracking
- [ ] Add status field for entry state management
- [ ] Define subscription source types and precedence order
- [ ] Create migration strategy from single to multi-entry system
- [ ] Update TypeScript types and interfaces

### Phase 2: Core Service Layer
- [ ] Create UserSubscriptionEntryService for CRUD operations
- [ ] Implement precedence calculation logic
- [ ] Add entry status management (apply/pause/expire)
- [ ] Create subscription aggregation service
- [ ] Update existing UserSubscriptionService to use new system

### Phase 3: Stripe Integration Updates
- [ ] Add Stripe subscription pausing functionality
- [ ] Update webhook handlers for multi-entry system
- [ ] Implement Stripe subscription resumption logic
- [ ] Handle Stripe cancellation with entry removal
- [ ] Ensure single Stripe entry per user constraint

### Phase 4: Perk System Integration
- [ ] Update perk application to create subscription entries
- [ ] Implement perk-based subscription precedence
- [ ] Add perk expiration handling
- [ ] Update PerkAwareSubscriptionService for new system

### Phase 5: Data Migration
- [ ] Create migration script for existing subscriptions
- [ ] Backup existing subscription data
- [ ] Convert single subscriptions to entry format
- [ ] Validate migration results
- [ ] Update production data safely

### Phase 6: Cron Job Implementation
- [ ] Create subscription entry expiration cron job
- [ ] Implement precedence recalculation on expiry
- [ ] Add Stripe subscription resumption logic
- [ ] Handle edge cases and error scenarios
- [ ] Add monitoring and logging

### Phase 7: Store & Hook Updates
- [ ] Update UserSubscriptionStore for multi-entry system
- [ ] Modify realtime hooks for entry subscriptions
- [ ] Update computed properties for aggregated state
- [ ] Ensure backward compatibility during transition

### Phase 8: Testing & Validation
- [ ] Create comprehensive test suite
- [ ] Test precedence scenarios
- [ ] Validate Stripe pausing/resumption
- [ ] Test perk application and expiration
- [ ] End-to-end integration testing

## Schema Design Details

### UserSubscriptionEntry Interface
```typescript
interface UserSubscriptionEntry extends BaseEntity {
  userId: string
  source: 'stripe' | 'perk' | 'giveaway'
  status: 'applied' | 'pending' | 'paused' | 'expired'
  precedence: number // 1=highest (perk), 2=medium (giveaway), 3=lowest (stripe)
  
  // Subscription details
  subscriptionPlan: SubscriptionPlan
  startDate: Timestamp
  endDate: Timestamp | null // null for indefinite
  
  // Source-specific data
  stripeData?: {
    customerId: string
    subscriptionId: string
    subscriptionStatus: SubscriptionStatus
    currentPeriodEnd: Timestamp
  }
  
  perkData?: {
    perkId: string
    appliedAt: Timestamp
    duration: number // in days
  }
  
  giveawayData?: {
    giveawayId: string
    duration: number // in days
  }
}
```

### Precedence Rules
1. **Perk subscriptions** (precedence: 1) - Highest priority
2. **Giveaway subscriptions** (precedence: 2) - Medium priority  
3. **Stripe subscriptions** (precedence: 3) - Lowest priority

### Status Management
- **applied**: Currently active and providing benefits
- **pending**: Waiting to be applied (lower precedence active)
- **paused**: Temporarily inactive (higher precedence active)
- **expired**: No longer valid

## Implementation Notes
- Only one entry per source type per user (especially Stripe)
- Stripe subscriptions are paused (not cancelled) when higher precedence entries are active
- Cron job runs daily to check for expirations and update precedence
- Migration must be atomic and reversible
- Maintain backward compatibility during transition period

## Success Criteria
- [ ] Multiple subscription sources work simultaneously
- [ ] Precedence system correctly prioritizes subscriptions
- [ ] Stripe subscriptions pause/resume automatically
- [ ] Perk-based subscriptions integrate seamlessly
- [ ] Existing functionality remains unaffected
- [ ] Performance impact is minimal
- [ ] Data integrity is maintained throughout migration
